# Use an official Python runtime as a parent image
FROM python:3.9-slim-bookworm

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends build-essential && rm -rf /var/lib/apt/lists/*

# Set the working directory in the container
WORKDIR /app

# Install uv
RUN pip install uv

# Copy the current project directory into the container at /app
COPY . .

# Sync dependencies using uv
RUN uv sync

# Run the application
CMD uv run python -m rvc_python api -p 5050 -l