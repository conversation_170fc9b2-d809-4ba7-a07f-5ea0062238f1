# CUDA

uv sync
uv pip remove torch torchaudio
uv pip install torch==2.1.1+cu121 torchaudio==2.1.1+cu121 --index-url https://download.pytorch.org/whl/cu121


# CLI

## CPU
uv run python -m rvc_python cli -i inputs/1093.mp3 -o output.wav -mp rvc_models/w_zaporozhec1_ru.pth -de cpu

## CUDA
uv run python -m rvc_python cli -i inputs/1093.mp3 -o output.wav -mp rvc_models/w_zaporozhec1_ru.pth -de cuda:0

# API

uv run python -m rvc_python api -p 5050 -l


1. Convert Audio
Endpoint: POST /convert
Description: Converts an audio file using the currently loaded model.
Request Body:
{
  "audio_data": "base64_encoded_audio"
}
Response: The converted audio file (WAV format)
Example:
import requests
import base64

url = "http://localhost:5050/convert"
with open("input.wav", "rb") as audio_file:
    audio_data = base64.b64encode(audio_file.read()).decode()

response = requests.post(url, json={"audio_data": audio_data})

with open("output.wav", "wb") as output_file:
    output_file.write(response.content)
2. List Available Models
Endpoint: GET /models
Description: Returns a list of all available models.
Response: JSON array of model names
Example:
response = requests.get("http://localhost:5050/models")
models = response.json()
print("Available models:", models)
3. Load a Model
Endpoint: POST /models/{model_name}
Description: Loads a specific model for use in conversions.
Response: Confirmation message
Example:
response = requests.post("http://localhost:5050/models/my_model")
print(response.json())
4. Get Current Parameters
Endpoint: GET /params
Description: Retrieves the current parameter settings.
Response: JSON object with current parameters
Example:
response = requests.get("http://localhost:5050/params")
print("Current parameters:", response.json())
5. Set Parameters
Endpoint: POST /params
Description: Updates the parameters for voice conversion.
Request Body:
{
  "params": {
    "f0method": "harvest",
    "f0up_key": 0,
    "index_rate": 0.5,
    "filter_radius": 3,
    "resample_sr": 0,
    "rms_mix_rate": 0.25,
    "protect": 0.33
  }
}
Response: Confirmation message
Example:
params = {
  "f0method": "harvest",
  "f0up_key": 2,
  "protect": 0.5
}
response = requests.post("http://localhost:5050/params", json={"params": params})
print(response.json())
6. Upload a New Model
Endpoint: POST /upload_model
Description: Uploads a new model (as a zip file) to the server.
Request: Multipart form data with a zip file
Response: Confirmation message
Example:
with open("new_model.zip", "rb") as zip_file:
    files = {"file": ("new_model.zip", zip_file)}
    response = requests.post("http://localhost:5050/upload_model", files=files)
print(response.json())
7. Set Computation Device
Endpoint: POST /set_device
Description: Sets the device (CPU/GPU) for computations.
Request Body:
{
  "device": "cuda:0"
}
Response: Confirmation message
Example:
response = requests.post("http://localhost:5050/set_device", json={"device": "cuda:0"})
print(response.json())
Model Management
Models are stored in the rvc_models directory by default. Each model should be in its own subdirectory and contain:

A .pth file (required): The main model file.
An .index file (optional): For improved voice conversion quality.
Example structure:

rvc_models/
├── model1/
│   ├── model1.pth
│   └── model1.index
└── model2/
    └── model2.pth
You can add new models by:

Manually placing them in the rvc_models directory.
Using the /upload_model API endpoint to upload a zip file containing the model files.
Using the /set_models_dir API endpoint to change the models directory dynamically.

# Docker

docker build -t rvc-api-image .
docker run -p 5050:5050 rvc-api-image